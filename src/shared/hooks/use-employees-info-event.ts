import { useAppDispatch } from '@/store'
import {
  tagsEmployeesApi,
  useDeleteEventIdMutation,
  useGetEmployeeEventInfoQuery,
} from '@/store/services/tags-employees-service'
import { useEffect, useState } from 'react'

const LOCAL_STORAGE_KEY = 'employeeEventId'

export const useEmployeesInfoEvent = () => {
  const [eventId, setEventId] = useState<string | null>()
  const [errorMessage, setErrorMessage] = useState<string | undefined>()
  const [uploading, setUploading] = useState<string | undefined>()
  const {
    data,
    error,
    isLoading: isEventLoading,
  } = useGetEmployeeEventInfoQuery(eventId || '', {
    skip: !eventId || uploading === 'not_started',
    pollingInterval: 5000,
  })
  const [deleteEventId] = useDeleteEventIdMutation()
  const handleEventId = () => {
    const id = localStorage.getItem(LOCAL_STORAGE_KEY)
    setEventId(id)
  }

  const handleUploading = () => {
    setUploading('in_process')
  }

  const dispatch = useAppDispatch()

  useEffect(() => {
    handleEventId()
  }, [])

  const handleClear = (shoulClearUploading: boolean = true) => {
    dispatch(
      tagsEmployeesApi.util.upsertQueryData('getEmployeeEventInfo', eventId || '', {
        metadata: undefined,
        id: '',
        type: '',
        status: '',
        error: {},
      }),
    )
    localStorage.removeItem(LOCAL_STORAGE_KEY)
    deleteEventId(eventId || '').unwrap()
    shoulClearUploading && setUploading('')
    setEventId(null)
  }

  useEffect(() => {
    if (data?.error && data?.error?.message) {
      setErrorMessage(data.error?.message)
      dispatch(
        tagsEmployeesApi.util.upsertQueryData('getEmployeeEventInfo', eventId || '', {
          metadata: undefined,
          id: '',
          type: '',
          status: '',
          error: {},
        }),
      )
      handleClear()
    }
  }, [data?.error])

  useEffect(() => {
    if (!!data?.metadata?.content?.uploading) {
      setUploading(data?.metadata?.content?.uploading)
    }

    if (data?.metadata?.content?.uploading === 'success') {
      handleClear(false)
    }
  }, [data?.metadata?.content?.uploading])

  return {
    status: data?.status,
    uploading,
    info: data?.metadata?.content?.info,
    errorLoading: error,
    error: data?.error?.message,
    errorMessage: errorMessage,
    eventId,
    handleEventId,
    fileName: data?.metadata?.metadata?.file_name,
    isEventLoading,
    handleClear,
    handleUploading,
    setUploading,
  }
}
