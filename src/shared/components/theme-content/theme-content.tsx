import { ThemeContentProps } from './theme-content.d'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Editor } from '../editor'
import {
  getEditorJsTools,
  getEditorPresentationTools,
  getEditorVideoTools,
} from '@/shared/configs/tools'
import { OutputBlockData } from '@editorjs/editorjs'
import { getPresentationDefaultData, getVideoDefaultData } from '@/pages/admin/create-theme/const'
import { CustomImport } from '../editor/tools/custom-import'
import { ManageQuestionsForm } from '@/features/manage-questions'
import { prepareArticleData, prepareSlideData, prepareVideoData } from './helper'
import { themeArticleApi, themeSlideApi, themeVideoApi } from '@/entities/themeCourse/model/api'
import styles from './theme-content.module.scss'
import classNamesBind from 'classnames/bind'
import { Loader } from '@/shared/ui'
import TrashBoldIcon from '@/shared/ui/Icon/icons/components/TrashBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import DeleteConfirmModal from '@/shared/modals/delete-confirm-modal/delete-confirm-modal'
import { useTranslation } from 'react-i18next'
import { useNotification } from '@/shared/contexts/notifications'
import { v4 as uuid } from 'uuid'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { TSlide } from '../editor/tools/custom-gallery-tool'
import { coursesApi } from '@/entities/courses'
import { addThemeCourseInSection } from '@/store/slices/new-course'
import { useAppDispatch } from '@/store'
import { URLS } from '@/shared/configs/urls'
import { EVENT_JSON_UPLOAD_KEY, EVENT_STATUS_LS_KEY } from './const'
import { setNavigateTo } from '../../../store/slices/theme-page-slice'
import {
  ThemeContentControlButtonsProvider,
  useThemeContentControlButtons,
} from './theme-content-context'

const cx = classNamesBind.bind(styles)

const fileBlockTypes = ['image', 'audio', 'video']

const useAppendThemeInCourseIfNeeded = ({ themeName }: { themeName: string }) => {
  const { t } = useTranslation('pages__create-theme')
  const { theme_id = '', course_id = '', section_id = '' } = useParams()
  const { pathname } = useLocation()
  const { add: addNotification, handleErrorResponse } = useNotification()
  const isAddedRef = useRef(false)

  const dispatch = useAppDispatch()
  const navigate = useNavigate()

  const [appendThemesInSection] = coursesApi.useAppendThemesInSectionMutation()

  const appendThemeInCourseIfNeeded = useCallback(
    async (cb?: () => void) => {
      const isForCourse = course_id && section_id
      const isOnlyForSection = !course_id && section_id
      if (!pathname.includes('/new/')) return
      if (isAddedRef.current) return

      if (isForCourse) {
        await appendThemesInSection({
          courseId: course_id,
          sectionId: section_id,
          themes: [theme_id],
        })
          .unwrap()
          .then(() => {
            addNotification({
              message: t('notify.create_theme'),
              status: 'success',
              id: uuid(),
            })

            isAddedRef.current = true
            dispatch(setNavigateTo(`${URLS.ADMIN_EDIT_COURSE_PAGE}${course_id}`))
            navigate(pathname.replace('/new/', '/'))
          })
          .catch(e => {
            handleErrorResponse(e)
          })

        return
      }

      if (isOnlyForSection) {
        dispatch(
          addThemeCourseInSection({
            sectionId: section_id,
            courseTheme: { id: theme_id, title: themeName, order: 0, index: 0 },
          }),
        )

        addNotification({
          message: t('notify.create_theme'),
          status: 'success',
          id: uuid(),
        })
        isAddedRef.current = true
        dispatch(setNavigateTo(URLS.ADMIN_CREATE_COURSE_PAGE))

        navigate(pathname.replace('/new/', '/'))

        return
      }

      if (cb) cb()
    },
    [
      themeName,
      theme_id,
      course_id,
      section_id,
      addNotification,
      appendThemesInSection,
      dispatch,
      handleErrorResponse,
      navigate,
      t,
    ],
  )

  return appendThemeInCourseIfNeeded
}

const Article: React.FC<ThemeContentProps.Props> = props => {
  const { data, setData, stepId, isNewStep, createTheme, themeName = '' } = props
  const { theme_id = '' } = useParams()

  const { add, handleErrorResponse } = useNotification()
  const { t } = useTranslation('pages__create-theme')
  const [isCustomLoading, setIsCustomLoading] = useState(false)

  const [createArticle, { isLoading: isCreateLoading }] = themeArticleApi.useCreateArticleMutation()
  const [updateBlocks, { isLoading: isUpdateLoading }] =
    themeArticleApi.useUpdateArticleBlocksMutation()
  const [uploadFile, { isLoading: isFileLoading }] = themeArticleApi.useUploadFileMutation()

  const appendThemeInCourseIfNeeded = useAppendThemeInCourseIfNeeded({ themeName })

  const checkIfNeedUpload = (block: OutputBlockData) =>
    (fileBlockTypes.includes(block.type) && block.data[block.type] instanceof File) ||
    (block.type === 'gallery' && block.data?.gallery?.find((item: TSlide) => !!item.file))

  const prepareBlocks = async () => {
    const newBlocks = []

    for (const block of data.blocks) {
      let newBlock = { ...block }
      const needUpload = checkIfNeedUpload(block)
      if (needUpload) {
        const body = new FormData()
        if (block.type === 'gallery') {
          block.data.gallery.forEach((image: TSlide) => {
            if (image.file) {
              body.append('files', image.file)
            }
          })
        } else {
          body.append('files', block.data[block.type])
        }
        await uploadFile({
          body,
        })
          .unwrap()
          .then(res => {
            newBlock = {
              ...block,
            }
            newBlock.data[block.type] =
              block.type === 'gallery'
                ? res.urls.map(i => ({
                    id: uuid(),
                    path: i,
                  }))
                : res.urls[0]
          })
      }
      newBlocks.push(newBlock)
    }

    return {
      ...data,
      blocks: newBlocks,
    }
  }

  const isFullLoadings = isFileLoading || isUpdateLoading || isCreateLoading || isCustomLoading

  const handleSubmit = async () => {
    if (!data || isFullLoadings) return

    setIsCustomLoading(true)
    if (!theme_id) {
      await createTheme()
      setTimeout(() => {
        setIsCustomLoading(false)
      }, 500)
      return
    }

    const newBlocks = await prepareBlocks()
    const preparedData = await prepareArticleData(newBlocks)

    try {
      if (!!stepId && !isNewStep) {
        await updateBlocks({
          themeId: theme_id,
          stepId,
          body: {
            blocks: preparedData ?? [],
          },
        }).unwrap()
        add({
          message: t('notify.update_step'),
          status: 'success',
          id: uuid(),
        })

        setTimeout(() => {
          setIsCustomLoading(false)
        }, 500)
      } else {
        const article = await createArticle({
          themeId: theme_id,
        }).unwrap()
        add({
          message: t('notify.create_step'),
          status: 'success',
          id: uuid(),
        })

        await updateBlocks({
          themeId: theme_id,
          stepId: article.id,
          body: {
            blocks: preparedData ?? [],
          },
        }).unwrap()
        await appendThemeInCourseIfNeeded(() => {
          add({
            message: t('notify.create_step'),
            status: 'success',
            id: uuid(),
          })
        })
        setTimeout(() => {
          setIsCustomLoading(false)
        }, 500)
      }
    } catch (e) {
      handleErrorResponse(e)
    }
  }

  return (
    <Editor
      data={data}
      setData={setData}
      // TODO: Write type for custom tools (Type - Tools)
      //@ts-ignore
      tools={getEditorJsTools()}
      onSubmit={handleSubmit}
      stepId={stepId}
      isLoading={isFullLoadings}
    />
  )
}

const Presentation: React.FC<ThemeContentProps.Props> = props => {
  const { data, setData, stepId, createTheme, isNewStep, themeName = '' } = props

  const defaultValue = data ? data : data !== null ? getPresentationDefaultData() : undefined
  const { theme_id = '' } = useParams()

  const { add, handleErrorResponse } = useNotification()
  const { t } = useTranslation('pages__create-theme')

  const [isCustomLoading, setIsCustomLoading] = useState(false)

  const appendThemeInCourseIfNeeded = useAppendThemeInCourseIfNeeded({ themeName })
  const [createSlide, { isLoading: isCreateLoading }] = themeSlideApi.useCreateSlideMutation()
  const [updateSlide, { isLoading: isUpdateLoading }] = themeSlideApi.useUpdateSlideMutation()

  const handleSubmit = async () => {
    if (!data) return

    setIsCustomLoading(true)
    if (!theme_id) {
      await createTheme()
      setIsCustomLoading(false)

      return
    }
    const preparedData = prepareSlideData(data)

    if (
      !data.blocks.find((b: OutputBlockData) => b.type === 'header') ||
      !data.blocks.find((b: OutputBlockData) => b.type === 'image')
    ) {
      add({
        id: uuid(),
        status: 'error',
        message: t('notify.slide_validation_error'),
        withoutAdditionalInfo: true,
      })
      setIsCustomLoading(false)

      return
    }

    try {
      if (!!stepId && !isNewStep) {
        updateSlide({
          themeId: theme_id,
          stepId,
          body: preparedData,
        }).then(() => {
          add({
            message: t('notify.update_step'),
            status: 'success',
            id: uuid(),
          })
          setIsCustomLoading(false)
        })
      } else {
        if (!preparedData.get('title') || !preparedData.get('background')) {
          add({
            id: uuid(),
            status: 'error',
            message: t('notify.slide_validation_error'),
            withoutAdditionalInfo: true,
          })

          return
        }
        createSlide({
          themeId: theme_id,
          body: preparedData,
        }).then(() => {
          appendThemeInCourseIfNeeded()
          setIsCustomLoading(false)
        })
        add({
          message: t('notify.create_step'),
          status: 'success',
          id: uuid(),
        })
      }
    } catch (e) {
      handleErrorResponse(e)
    } finally {
      setIsCustomLoading(false)
    }
  }

  return (
    <Editor
      data={defaultValue}
      setData={setData}
      // TODO: Write type for custom tools (Type - Tools)
      //@ts-ignore
      tools={getEditorPresentationTools()}
      onSubmit={handleSubmit}
      stepId={stepId}
      isLoading={isCreateLoading || isUpdateLoading || isCustomLoading}
    />
  )
}

const Video: React.FC<ThemeContentProps.Props> = props => {
  const { data, setData, stepId, createTheme, isNewStep, themeName = '' } = props
  const defaultValue = data ? data : data !== null ? getVideoDefaultData() : undefined
  const { add, handleErrorResponse } = useNotification()
  const { t } = useTranslation('pages__create-theme')

  const { theme_id = '' } = useParams()

  const appendThemeInCourseIfNeeded = useAppendThemeInCourseIfNeeded({ themeName })
  const [createVideo, { isLoading: isCreateLoading }] = themeVideoApi.useCreateVideoMutation()
  const [updateVideo, { isLoading: isUpdateLoading }] = themeVideoApi.useUpdateVideoMutation()

  const handleSubmit = () => {
    if (!data) return

    if (!theme_id) {
      return createTheme()
    }

    const preparedData = prepareVideoData(data)
    if (
      !data.blocks.find((b: OutputBlockData) => b.type === 'header') ||
      !data.blocks.find((b: OutputBlockData) => b.type === 'video')
    ) {
      add({
        id: uuid(),
        status: 'error',
        message: t('notify.video_validation_error'),
        withoutAdditionalInfo: true,
      })

      return
    }
    try {
      if (!!stepId && !isNewStep) {
        updateVideo({
          themeId: theme_id,
          stepId,
          body: preparedData,
        })
        add({
          message: t('notify.update_step'),
          status: 'success',
          id: uuid(),
        })
      } else {
        if (!preparedData.get('title') || !preparedData.get('video')) {
          add({
            id: uuid(),
            status: 'error',
            message: t('notify.video_validation_error'),
            withoutAdditionalInfo: true,
          })

          return
        }
        createVideo({
          themeId: theme_id,
          body: preparedData,
        })
          .unwrap()
          .then(() => appendThemeInCourseIfNeeded())
        add({
          message: t('notify.create_step'),
          status: 'success',
          id: uuid(),
        })
      }
    } catch (e) {
      handleErrorResponse(e)
    }
  }

  return (
    <Editor
      data={defaultValue}
      setData={setData}
      // TODO: Write type for custom tools (Type - Tools)
      //@ts-ignore
      tools={getEditorVideoTools()}
      onSubmit={handleSubmit}
      stepId={stepId}
      isLoading={isCreateLoading || isUpdateLoading}
    />
  )
}

const Scorm: React.FC<ThemeContentProps.Props> = props => {
  const { data, stepId } = props
  const { t } = useTranslation('pages__create-theme')

  if (!data || !data.path) return null

  return (
    <div className={cx('iframeWrapper')} key={stepId}>
      <iframe
        allowFullScreen
        width='100%'
        height='100%'
        title={t('commons:course')}
        src={`${data.path}`}
        sandbox='allow-storage-access-by-user-activation allow-scripts allow-same-origin allow-popups'
      />
    </div>
  )
}

export const ThemeContent: React.FC<ThemeContentProps.Props> = props => {
  const { data, type, stepId, createTheme, isNewStep, themeName = '', themeData } = props

  const appendThemeInCourseIfNeeded = useAppendThemeInCourseIfNeeded({ themeName })

  const [openDelete, setOpenDelete] = useState<boolean>(false)

  useEffect(() => {
    console.log(stepId, 'step ID')
  }, [stepId])

  useEffect(() => {
    console.log(data, 'data')
  }, [data])

  const steps = {
    article: <Article {...props} />,
    slide: <Presentation {...props} />,
    video: <Video {...props} />,
    quiz:
      !isNewStep && !data ? null : (
        <React.Fragment key={stepId}>
          <ManageQuestionsForm
            initialState={data}
            stepId={stepId}
            createTheme={createTheme}
            appendThemeInCourseIfNeeded={appendThemeInCourseIfNeeded}
            isNewStep={isNewStep}
            themeId={themeData?.id}
          />
        </React.Fragment>
      ),
    import: (
      <React.Fragment key={stepId}>
        <CustomImport
          stepId={stepId}
          createTheme={createTheme}
          appendThemeInCourseIfNeeded={appendThemeInCourseIfNeeded}
        />
      </React.Fragment>
    ),
    scorm: <Scorm {...props} />,
  }

  const openDeleteModal = () => setOpenDelete(true)

  useEffect(() => {
    return () => {
      localStorage.removeItem(EVENT_STATUS_LS_KEY)
      localStorage.removeItem(EVENT_JSON_UPLOAD_KEY)
    }
  }, [])

  const isJsonUploading = localStorage.getItem(EVENT_JSON_UPLOAD_KEY) === 'uploading'
  const isThemeFetching = isJsonUploading && isNewStep && !data && type === 'import'

  useEffect(() => {
    if (data && isJsonUploading) {
      localStorage.removeItem(EVENT_JSON_UPLOAD_KEY)
    }
  }, [data, isJsonUploading])

  if (isThemeFetching) {
    return <Loader size='56' className={cx('loader')} loading />
  }

  return type ? (
    <ThemeContentControlButtonsProvider>
      <ThemeContentInner
        {...props}
        steps={steps}
        openDeleteModal={openDeleteModal}
        openDelete={openDelete}
        setOpenDelete={setOpenDelete}
      />
    </ThemeContentControlButtonsProvider>
  ) : null
}

const ThemeContentInner: React.FC<
  ThemeContentProps.Props & {
    steps: Record<string, React.ReactNode>
    openDeleteModal: () => void
    openDelete: boolean
    setOpenDelete: (open: boolean) => void
  }
> = ({ type, steps, openDeleteModal, openDelete, setOpenDelete, deleteStep }) => {
  const { t } = useTranslation('pages__create-theme')
  const { controlButtons } = useThemeContentControlButtons()

  const controlButtonsArray = useMemo(
    () =>
      Object.entries(controlButtons).map(([key, button]) =>
        React.cloneElement(button as React.ReactElement, { key }),
      ),
    [controlButtons],
  )

  return (
    <div className={cx('wrapper')}>
      <div className={cx('controlButtonsContainer')}>
        {controlButtonsArray}
        {type !== 'import' && (
          <IconWrapper
            size='24'
            color='gray80'
            className={cx('icon', 'trash')}
            onClick={openDeleteModal}
          >
            <TrashBoldIcon />
          </IconWrapper>
        )}
      </div>
      {steps[type!]}
      {openDelete && (
        <DeleteConfirmModal
          open={openDelete}
          setOpen={setOpenDelete}
          title={t('step_delete')}
          description={t('step_delete_hint')}
          onConfirm={deleteStep}
        />
      )}
    </div>
  )
}
