import styles from './create-theme.module.scss'
import classNamesBind from 'classnames/bind'
import { useEffect, useState, useRef, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { OutputData } from '@editorjs/editorjs'
import { Breadcrumbs, Button, Input, PageTitle } from '@/shared/ui'
import {
  Step,
  StepsToolbar,
  TMinifiedStepType,
} from '@/shared/components/editor/steps-toolbar/steps-toolbar'
import { ThemeContent } from '@/shared/components/theme-content'
import { themeApi, themeStepsApi } from '@/entities/themeCourse/model/api'
import { createSearchParams, useNavigate, useParams } from 'react-router-dom'
import { ManageQuestionsFormState } from '@/features/manage-questions/views/manage-questions-form'
import {
  transformArticleData,
  transformPresentationData,
  transformVideoData,
} from '@/shared/components/theme-content/helper'
import {
  ICreateThemeResponse,
  TArticleStep,
  TQuizStep,
  TScormStep,
  TSlideStep,
  TStep,
  TVideoStep,
} from '@/entities/themeCourse/model/types'
import i18n from 'i18next'
import ReplaceModal from '@/shared/modals/replace-modal/replace-modal'
import { useNotification } from '@/shared/contexts/notifications'
import { v4 as uuid } from 'uuid'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  DEFAULT_BREADCRUMBS,
  resetThemePageState,
  selectNavigateTo,
  selectThemePageBreadcrumbs,
  setThemePageBreadcrumbs,
} from '@/store/slices/theme-page-slice'
import { ThemePreview } from '@/entities/themeCourse/ui/theme-preview'
import { URLS } from '../../../shared/configs/urls'
import { updateThemeTitleInSection } from '@/store/slices/new-course'
import { SaveAsNewThemeButton } from './save-theme-as-new'
import { useUserOrganizationId } from '../../../entities/employee'

const cx = classNamesBind.bind(styles)

export type DataType = OutputData | ManageQuestionsFormState | { path: string } | null

export const CreateTheme = () => {
  const { theme_id = '', section_id = '', course_id = '' } = useParams()
  const dispatch = useAppDispatch()

  useEffect(() => {
    i18n.loadNamespaces([
      'components__custom-image',
      'components__custom-audio',
      'components__custom-video',
    ])

    return () => {
      dispatch(resetThemePageState())
    }
  }, [dispatch])

  const { t } = useTranslation('pages__create-theme')
  const navigate = useNavigate()

  const breadcrumbsList = useAppSelector(selectThemePageBreadcrumbs)

  const { data: themeData } = themeApi.useGetThemeByIdQuery(theme_id, {
    skip: !theme_id,
  })

  const [createTheme, { isLoading: isCreateThemeLoading }] = themeApi.useCreateThemeMutation()
  const [deleteStep] = themeStepsApi.useDeleteStepMutation()
  const [updateThemeName] = themeApi.useUpdateThemeMutation()
  const userOrganizationId = useUserOrganizationId()

  const navigateTo = useAppSelector(selectNavigateTo)

  const [data, setData] = useState<DataType>()
  const [name, setName] = useState<string>('')
  const [activeStep, setActiveStep] = useState<Step>()
  const [minifiedSteps, setMinifiedSteps] = useState<Step[]>([])
  const [isOpenReplace, setIsOpenReplace] = useState<boolean>(false)

  const isInitializedRef = useRef(false)
  const lastStepsLengthRef = useRef(0)
  const lastThemeIdRef = useRef<string>('')
  const lastActiveStepIdRef = useRef<string>('')

  const { add, handleErrorResponse } = useNotification()

  const onNameBlur = () => {
    if (!theme_id) return

    setIsOpenReplace(true)
  }

  const handleReplace = () => {
    if (!theme_id) return

    updateThemeName({
      themeId: theme_id,
      title: name,
    })

    dispatch(updateThemeTitleInSection({ sectionId: section_id, themeId: theme_id, title: name }))
  }

  useEffect(() => {
    if (!themeData) {
      dispatch(setThemePageBreadcrumbs(DEFAULT_BREADCRUMBS))

      return
    }

    if (themeData.title !== name) setName(themeData.title)

    dispatch(
      setThemePageBreadcrumbs([
        { id: URLS.ADMIN_LEARNING_THEMES_PAGE, text: 'commons:themes', clickable: true },
        {
          id: '/lk/admin/learning/courses/create',
          text: t('commons:theme_settings'),
          clickable: false,
        },
      ]),
    )
  }, [themeData, dispatch, t])

  const handleActiveData = ({ activeStep, stepData }: { activeStep?: Step; stepData?: TStep }) => {
    const step = stepData ?? themeData?.steps.find(s => s.id === activeStep?.id)

    if (step) {
      switch (step.type) {
        case 'slide': {
          const slideData = transformPresentationData(step as TSlideStep)
          setData(slideData)
          break
        }

        case 'video': {
          const videoData = transformVideoData(step as TVideoStep)
          setData(videoData)
          break
        }

        case 'quiz': {
          setData({
            questions: (step as TQuizStep).questions,
            settings: (step as TQuizStep).settings,
          })
          break
        }
        case 'article': {
          const articleData = transformArticleData(step as TArticleStep)
          setData(articleData)
          break
        }
        case 'scorm': {
          setData({
            path: (step as TScormStep).path,
          })
        }
      }
    } else if (activeStep?.isNew) {
      // Для новых шагов устанавливаем null, чтобы компоненты использовали дефолтные данные
      setData(null)
    } else {
      setData(undefined)
    }
  }

  useEffect(() => {
    if (!theme_id) return

    dispatch(
      setThemePageBreadcrumbs([
        { id: URLS.ADMIN_LEARNING_THEMES_PAGE, text: 'commons:themes', clickable: true },
        {
          id: '/lk/admin/learning/courses/create',
          text: t('commons:theme_settings'),
          clickable: false,
        },
      ]),
    )
  }, [theme_id, t, dispatch])

  useEffect(() => {
    if (!themeData) {
      isInitializedRef.current = false
      lastStepsLengthRef.current = 0
      lastThemeIdRef.current = ''
      lastActiveStepIdRef.current = ''
      return
    }

    const currentStepsLength = themeData.steps?.length || 0
    const currentThemeId = themeData.id || ''
    const currentActiveStepId = activeStep?.id || ''

    const hasRealChanges =
      lastStepsLengthRef.current !== currentStepsLength || lastThemeIdRef.current !== currentThemeId

    if (!!themeData && !!themeData.steps?.length) {
      const needsInitialization = !activeStep || !isInitializedRef.current || hasRealChanges

      if (needsInitialization) {
        const firstStep = themeData.steps[0]

        if (firstStep) {
          switch (firstStep.type) {
            case 'slide': {
              const slideData = transformPresentationData(firstStep as TSlideStep)
              setData(slideData)
              break
            }
            case 'video': {
              const videoData = transformVideoData(firstStep as TVideoStep)
              setData(videoData)
              break
            }
            case 'quiz': {
              setData({
                questions: (firstStep as TQuizStep).questions,
                settings: (firstStep as TQuizStep).settings,
              })
              break
            }
            case 'article': {
              const articleData = transformArticleData(firstStep as TArticleStep)
              setData(articleData)
              break
            }
            case 'scorm': {
              setData({
                path: (firstStep as TScormStep).path,
              })
            }
          }
        }

        setActiveStep({
          id: firstStep.id,
          type: firstStep.type as TMinifiedStepType,
          isNew: false,
        })

        const minified = themeData.steps.map(s => ({
          id: s.id,
          type: s.type,
          isNew: false,
        }))
        setMinifiedSteps(minified as Step[])

        isInitializedRef.current = true
        lastStepsLengthRef.current = currentStepsLength
        lastThemeIdRef.current = currentThemeId
        lastActiveStepIdRef.current = currentActiveStepId
      } else if (hasRealChanges) {
        const minified = themeData.steps.map(s => ({
          id: s.id,
          type: s.type,
          isNew: false,
        }))
        setMinifiedSteps(minified as Step[])
        lastStepsLengthRef.current = currentStepsLength
        lastThemeIdRef.current = currentThemeId
      } else {
        lastActiveStepIdRef.current = currentActiveStepId
      }
    }

    if (!themeData?.steps?.length && !minifiedSteps.length) {
      setData(undefined)
      setActiveStep(undefined)
      setMinifiedSteps([])
      isInitializedRef.current = false
      lastStepsLengthRef.current = 0
      lastThemeIdRef.current = ''
      lastActiveStepIdRef.current = ''
    }
  }, [themeData?.steps?.length, themeData?.id, themeData?.steps, activeStep, themeData])

  const handleInput = (value: string) => setName(value)

  const handleCreateFirstStepClick = () => {
    if (!name || isCreateThemeLoading) {
      return
    }

    createTheme({
      title: name,
      organization_id: userOrganizationId ?? '',
    })
      .unwrap()
      .then((res: ICreateThemeResponse) => {
        const isForCourse = course_id && section_id
        const isOnlyForSection = !course_id && section_id

        let pathToRedirect = `/lk/admin/learning/themes/${res.id}`

        if (isForCourse) {
          pathToRedirect = `/lk/admin/learning/themes/new/${res.id}/course/${course_id}/section/${section_id}`
        }

        if (isOnlyForSection) {
          pathToRedirect = `/lk/admin/learning/themes/new/${res.id}/section/${section_id}`
        }

        navigate({
          pathname: pathToRedirect,
          search: createSearchParams({
            openStepModal: 'true',
          }).toString(),
        })
      })
      .catch(e => handleErrorResponse(e))
  }

  const handleCreateTheme = async () => {
    if (!name || isCreateThemeLoading) return

    try {
      await createTheme({
        title: name,
        organization_id: userOrganizationId ?? '',
      })
        .unwrap()
        .then((res: ICreateThemeResponse) => {
          add({
            message: t('notify.create_theme'),
            status: 'success',
            id: uuid(),
          })

          if (!course_id) navigate(`/lk/admin/learning/themes/${res.id}`)
        })
    } catch (e) {
      handleErrorResponse(e)
    }
  }

  const handleDeleteStep = async () => {
    const index = minifiedSteps.findIndex(item => item.id === activeStep?.id)
    if (!activeStep?.isNew && theme_id && activeStep?.id) {
      try {
        await deleteStep({
          themeId: theme_id,
          stepId: activeStep.id,
        }).then(() => {
          add({
            message: t('notify.delete_step'),
            status: 'success',
            id: uuid(),
          })
        })
      } catch (e) {
        handleErrorResponse(e)
      }
    } else {
      const newMinified = [...minifiedSteps]
      newMinified.splice(index, 1)
      setMinifiedSteps(newMinified)
      if (newMinified.length > 0) {
        handleActiveStep(newMinified[0])
      } else {
        setActiveStep(undefined)
        setData(undefined)
      }
    }
  }

  const handleActiveStep = (item: Step) => {
    handleActiveData({ activeStep: item })
    setActiveStep(item)
  }

  const getSaveButtonDisabled = () => {
    if (isCreateThemeLoading) return isCreateThemeLoading
    if (themeData && !themeData?.can_edit) return !themeData?.can_edit

    if (section_id) return !name || !section_id || !minifiedSteps.length

    return !!theme_id || isCreateThemeLoading || !name
  }

  return (
    <div className={cx('wrapper')}>
      <Breadcrumbs items={breadcrumbsList} className={cx('breadcrumbs')} />
      <div className={cx('title__wrapper')}>
        <PageTitle>
          {theme_id ? t('commons:theme_settings') : t('commons:theme_creation')}
        </PageTitle>
        {(navigateTo || course_id || (section_id && theme_id)) && (
          <Button
            onClick={() => {
              if (navigateTo) {
                navigate(navigateTo)
                return
              }
              if (course_id) {
                navigate(`${URLS.ADMIN_EDIT_COURSE_PAGE}${course_id}`)
                return
              }
              if (section_id) navigate(URLS.ADMIN_CREATE_COURSE_PAGE)
            }}
            size='medium'
          >
            {t('go_to_the_course')}
          </Button>
        )}
      </div>
      <div className={cx('block')}>
        <div className={cx('top')}>
          <div className={cx('name')}>{t('theme')}</div>
        </div>
        <Input
          fullWidth
          value={name}
          onChange={handleInput}
          label={t('commons:name')}
          placeholder={t('commons:name')}
          classNameWrapper={cx('itemWrapper')}
          onBlur={onNameBlur}
          disabled={themeData && !themeData?.can_edit}
        />

        {themeData && course_id && section_id && themeData?.can_edit && (
          <SaveAsNewThemeButton name={name} />
        )}
      </div>
      <div className={cx('content')}>
        {themeData?.can_edit || (section_id && !themeData) ? (
          <>
            <StepsToolbar
              steps={minifiedSteps}
              setSteps={setMinifiedSteps}
              activeStep={activeStep}
              setActive={handleActiveStep}
              themeId={theme_id}
              allowSwap={true}
              onCreateFirstStepClick={handleCreateFirstStepClick}
              isLoading={isCreateThemeLoading}
              isDisabled={!name}
            />
            <ThemeContent
              data={data}
              setData={setData}
              type={activeStep?.type}
              stepId={activeStep?.id}
              createTheme={handleCreateTheme}
              themeData={themeData}
              themeName={name}
              isNewStep={activeStep?.isNew}
              deleteStep={handleDeleteStep}
            />
          </>
        ) : (
          <ThemePreview themeData={themeData} />
        )}
        {!minifiedSteps.length && (
          <Button
            disabled={getSaveButtonDisabled()}
            onClick={handleCreateTheme}
            size='big'
            color='green'
            className={cx('button')}
          >
            {t('commons:save')}
          </Button>
        )}
      </div>
      {isOpenReplace && (
        <ReplaceModal
          open={isOpenReplace}
          setOpen={setIsOpenReplace}
          title={t('replace_name')}
          onConfirm={handleReplace}
          submitText={t('commons:replace')}
        />
      )}
    </div>
  )
}
