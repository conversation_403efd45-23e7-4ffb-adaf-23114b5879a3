import { createAsyncThunk, createSelector, UnknownAction } from '@reduxjs/toolkit'
import { RootState } from '../../store'
import { ETagType } from '../../../shared/types/enums'
import {
  initialDepartmentsTargetAdapter,
  initialMessagesAdapter,
  initialUsersTargetAdapter,
  setActiveTag,
  setPage,
} from './tags-slice'
import { TAG_LIFE_DAYS_LIMIT } from '@/shared/constants'
import { tagsEmployeesApi } from '@/store/services/tags-employees-service'
import { checkTagIsCustom } from '@/shared/helpers/organization'
import { coursesByTagApi } from '../../services/endpoints/new-tags-endpoint'
import { ITag, ITagsWithData } from '@/shared/types/store/tag'

// eslint-disable-next-line i18n/no-russian-character
const NEW_EMPLOYEE_TITLE = 'Новый сотрудник'

const selectTags = createSelector(
  (state: RootState) => state,
  state => state.tags.tags,
)
const selectTagsAutophishingSettingsById = createSelector(
  (state: RootState, id: UUID) => ({ state, id }),
  ({ state, id }) => state.tags.authophishingSettingsByIdData?.find(item => item.id === id),
)

const selectTagsAutophishingId = createSelector(
  (state: RootState) => state,
  state => (state.tags.activeTag?.title === NEW_EMPLOYEE_TITLE ? state.tags.activeTag?.id : ''),
)

const selectTagsTurnOff = createSelector(
  (state: RootState) => state,
  state => state.tags.turnOff,
)

const selectTagsCourses = createSelector(
  (state: RootState) => state,
  state => state.tags.courses,
)

const selectTagsMessages = createSelector(
  (state: RootState) => state,
  state => state.tags.messages,
)

const selectTagsPhishingTemplates = createSelector(
  (state: RootState) => state,
  state => state.tags.phishingTemplates,
)

const selectTagsModalValues = createSelector(
  (state: RootState) => state,
  state => state.tags.modals,
)

const selectTagLifeDays = createSelector(
  (state: RootState) => state,
  state => state.tags.tagLifeDays ?? TAG_LIFE_DAYS_LIMIT,
)

const selectInitialLifeDays = createSelector(
  (state: RootState) => state,
  state => state.tags.initialTagLifeDays ?? TAG_LIFE_DAYS_LIMIT,
)

const selectRiskLevelMax = createSelector(
  (state: RootState) => state,
  state => state.tags.riskLevelMax ?? 10,
)

const selectRiskLevelMin = createSelector(
  (state: RootState) => state,
  state => state.tags.riskLevelMin ?? 0,
)

const selectActiveTag = createSelector(
  (state: RootState) => state,
  state => state.tags.activeTag,
)

const selectTagsActions = createSelector(
  (state: RootState) => state,
  state => state.tags.actions ?? [],
)

const selectTagsSelectedCourses = createSelector(
  (state: RootState) => state,
  state => state.tags.selectedCourses ?? [],
)

const selectTagsSelectedScormCourses = createSelector(
  (state: RootState) => state,
  state => state.tags.selectedScormCourses ?? [],
)

const selectAllTags = createSelector(
  (state: RootState) => state,
  state => {
    const allQueriesParams = tagsEmployeesApi.util.selectCachedArgsForQuery(
      state,
      'getOrganizationTags',
    )
    const lastParams = allQueriesParams[allQueriesParams.length - 1]
    const tags: Record<ITag['id'], ITag> = {}
    let lastResult = tagsEmployeesApi.endpoints.getOrganizationTags.select(lastParams)(state)

    allQueriesParams.forEach(param => {
      const res = tagsEmployeesApi.endpoints.getOrganizationTags.select(param)(state)
      const tagsData = res?.data?.data?.filter(tagItem => tagItem) ?? []
      lastResult = res
      tagsData.forEach(tag => {
        tags[tag.id] = tag
      })
    })

    return {
      ...lastResult?.data,
      data: Object.values(tags ?? {}),
    }
  },
)

const selectRiskGroupTags = createSelector(
  (state: RootState) => state,
  state => {
    const allTagsData = selectAllTags(state)

    return allTagsData?.data?.filter(tagItem => tagItem.type === ETagType.risk_group) ?? []
  },
)

const selectLoadedCourses = createSelector(
  (state: RootState, tagId?: UUID) => ({ state, tagId: tagId }),
  ({ state, tagId }) =>
    coursesByTagApi.endpoints.getCoursesByTag.select({ tag_id: tagId || 'abc' })(state)?.data,
)

const selectOtherTags = createSelector(
  (state: RootState) => state,
  state => {
    const allTagsData = selectAllTags(state)

    return allTagsData?.data?.filter(tagItem => !checkTagIsCustom(tagItem)) ?? []
  },
)

const selectCustomTags = createSelector(
  (state: RootState) => state,
  state => {
    const allTagsData = selectAllTags(state)

    return {
      ...allTagsData,
      data: allTagsData?.data?.filter(tagItem => checkTagIsCustom(tagItem)) ?? [],
    }
  },
)

const selectHasCourseAction = createSelector(
  (state: RootState) => state,
  state => Boolean(state.tags.actions?.filter(a => a.type === 'course').length),
)

const selectInitialMessages = initialMessagesAdapter.getSelectors<RootState>(
  state => state.tags.initialMessages || initialMessagesAdapter.getInitialState(),
)

const selectUsersTagTarget = createSelector(
  (state: RootState) => state,
  state => state.tags.tagTargets.users,
)

const selectInitialUsersTagTarget = initialUsersTargetAdapter.getSelectors<RootState>(
  state => state.tags.tagTargets.initialUsers,
)

const selectInitialUsersTagUsers = createSelector(
  (state: RootState) => state,
  state => state.tags.tagTargets?.users ?? [],
)

const selectExludeUsersIds = createSelector(
  (state: RootState) => state,
  state => state.tags.tagTargets.excludeUsersIds ?? [],
)

const selectDepartmentsTagTarget = createSelector(
  (state: RootState) => state,
  state => state.tags.tagTargets.departments,
)

const selectInitialDepartmentsTagTarget = initialDepartmentsTargetAdapter.getSelectors<RootState>(
  state => state.tags.tagTargets.initialDepartments,
)

const selectPage = createSelector(
  (state: RootState) => state,
  state => state.tags.page,
)

export const deleteTagById = createAsyncThunk(
  'tags/deleteTagById-async-thunk',
  async (tagId: UUID, { dispatch, getState }) => {
    await dispatch(tagsEmployeesApi.endpoints.deleteCustomTag.initiate(tagId)).unwrap()

    const state = getState() as RootState

    const activeTag = selectActiveTag(state)

    if (activeTag?.id === tagId) {
      const allCustomTags = selectCustomTags(state)
      dispatch(setActiveTag(allCustomTags?.data?.[0] || null))
    }
  },
)

export const createTag = createAsyncThunk(
  'tags/create-tag-async-thunk',
  async (tagData: ITag, { dispatch }) => {
    await dispatch(tagsEmployeesApi.endpoints.createCustomTag.initiate(tagData)).unwrap()
  },
)

export const invalidateAllTags = createAsyncThunk(
  'tags/invalidateAllTags-async-thunk',
  async (_, { dispatch, getState }) => {
    const state = getState() as RootState

    const allQueriesParams = tagsEmployeesApi.util.selectCachedArgsForQuery(
      state,
      'getOrganizationTags',
    )

    allQueriesParams.forEach(param => {
      // ts-ignore
      dispatch(
        tagsEmployeesApi.util.upsertQueryData(
          'getOrganizationTags',
          param,
          undefined as unknown as ITagsWithData,
        ) as unknown as UnknownAction,
      )
    })
    dispatch(setPage(1))
    dispatch(tagsEmployeesApi.util.invalidateTags(['tags']))
  },
)

export const editTagById = createAsyncThunk(
  'tags/editTagById-async-thunk',
  async (
    tagData: {
      tagId: UUID
      body: {
        title: string
        color: string
      }
    },
    { dispatch, getState },
  ) => {
    await dispatch(tagsEmployeesApi.endpoints.editCustomTag.initiate(tagData)).unwrap()

    const state = getState() as RootState

    const allQueriesParams = tagsEmployeesApi.util.selectCachedArgsForQuery(
      state,
      'getOrganizationTags',
    )

    allQueriesParams.forEach(param => {
      const res = tagsEmployeesApi.endpoints.getOrganizationTags.select(param)(state)

      const newTagsData = {
        ...res?.data,
        total_count: Number(res?.data?.total_count),
        limit: Number(res?.data?.limit),
        offset: Number(res?.data?.offset),
        data:
          res?.data?.data?.map(tagItem => {
            if (tagItem.id === tagData.tagId) {
              return {
                ...tagItem,
                title: tagData.body.title,
                color: tagData.body.color,
              }
            }
            return tagItem
          }) ?? [],
      } as ITagsWithData

      dispatch(
        tagsEmployeesApi.util.upsertQueryData(
          'getOrganizationTags',
          param,
          newTagsData,
        ) as unknown as UnknownAction,
      )
    })
  },
)

export {
  selectPage,
  selectExludeUsersIds,
  selectInitialUsersTagUsers,
  selectActiveTag,
  selectTagsAutophishingId,
  selectRiskGroupTags,
  selectOtherTags,
  selectCustomTags,
  selectTagLifeDays,
  selectInitialLifeDays,
  selectRiskLevelMax,
  selectRiskLevelMin,
  selectTagsModalValues,
  selectTags,
  selectTagsCourses,
  selectTagsMessages,
  selectTagsPhishingTemplates,
  selectTagsSelectedCourses,
  selectTagsSelectedScormCourses,
  selectHasCourseAction,
  selectLoadedCourses,
  selectTagsTurnOff,
  selectTagsActions,
  selectTagsAutophishingSettingsById,
  selectInitialMessages,
  selectUsersTagTarget,
  selectInitialUsersTagTarget,
  selectDepartmentsTagTarget,
  selectInitialDepartmentsTagTarget,
  selectAllTags,
}
